# Copyright 2022 ACSONE SA/NV
# License LGPL-3.0 or later (http://www.gnu.org/licenses/LGPL).
from typing import Annotated, Any, List

from a2wsgi import ASGIMiddleware
from fastapi import FastAPI

from odoo import _, api, fields, models, tools
from odoo.api import Environment
from odoo.exceptions import ValidationError


from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import APIKeyHeader

from ..dependencies import (odoo_env, fastapi_endpoint_impl)
from ..routers import srm_b2c_router, crm_router,apm_router,b2b_prm_router
from fastapi.middleware.cors import CORSMiddleware
from .. import dependencies


class FastapiEndpoint(models.Model):
    _name = "fastapi.endpoint"
    _inherit = ["fastapi.endpoint", "th.intermediate.table"]

    th_log_api_ids = fields.One2many('th.log.api', 'th_fastapi_endpoint_id')
    th_auth_method = fields.Selection([("api_key", "Api Key")], string="Auth method")
    th_api_key = fields.Char(string="API Key")
    app = fields.Selection(selection_add=[('v1', 'B2B V1')], ondelete={"v1": "cascade"})

    def _get_fastapi_routers(self) -> List[APIRouter]:
        # Trả về router đã định tuyến theo trường "app" được cấu hình
        routers = super()._get_fastapi_routers()
        if self.app == 'v1':
            return routers + [srm_b2c_router, crm_router, apm_router, b2b_prm_router]
        return routers

    @api.model
    def _fastapi_app_fields(self) -> List[str]:
        field = super()._fastapi_app_fields()
        # Khi thêm 1 trường mới thì phải thêm ở đây(theo hướng dẫn của fastapi)
        field.append("th_auth_method")
        return field
    
    @api.model
    @tools.ormcache("root_path")
    def get_app(self, root_path):
        """
            Override lại phương thức get_app để có thể gộp nhiều endpoint cùng root_path lại với nhau
        """
        records = self.search([("root_path", "=", root_path)])
        if not records:
            return None

        # Nếu chỉ có một endpoint, sử dụng logic cũ
        if len(records) == 1:
            app = FastAPI()
            app.mount(records[0].root_path, records[0]._get_app())
            self._clear_fastapi_exception_handlers(app)
            return ASGIMiddleware(app)

        # Nếu có nhiều endpoint với cùng root_path, gộp chúng lại
        app = FastAPI()
        merged_fastapi_app = self._merge_multiple_endpoints(records)
        app.mount(root_path, merged_fastapi_app)
        self._clear_fastapi_exception_handlers(app)
        return ASGIMiddleware(app)
    
    def _get_app(self):
        self.clear_caches()
        # check các link website gọi vào API xem có được phép gọi vào api hay không.
        app = super()._get_app()
        # Kiểm tra lại xem router có sử dụng phương thức bảo mật không (api_key)
        if self.app in ['v1']:
            auth_fast_endpoint = (
                th_api_key_based
            )
            app.dependency_overrides[
                fastapi_endpoint_impl
            ] = auth_fast_endpoint
        return app
    
    
    def _merge_multiple_endpoints(self, records):
        """
        Gộp nhiều endpoint có cùng root_path thành một FastAPI app duy nhất
        """
        # Sử dụng endpoint đầu tiên làm base để lấy thông tin chung
        base_endpoint = records[0]
        
        # Tạo base app từ endpoint đầu tiên
        merged_app = base_endpoint._get_app()
        
        # Cập nhật title và description để phản ánh việc merge
        merged_app.title = f"Merged API - {base_endpoint.root_path}"
        merged_app.description = f"Merged FastAPI application from {len(records)} endpoints"
        
        # Gộp routers từ các endpoints còn lại
        for record in records[1:]:
            # Thêm routers từ endpoint này
            for router in record._get_fastapi_routers():
                merged_app.include_router(router=router)
            
            # Gộp dependency overrides
            overrides = record._get_app_dependencies_overrides()
            merged_app.dependency_overrides.update(overrides)
            
            # Áp dụng custom modifications nếu có
            if hasattr(record, '_apply_additional_app_modifications'):
                record._apply_additional_app_modifications(merged_app)
        
        return merged_app

    @api.model
    @tools.ormcache("root_path")
    def get_uid(self, root_path):
        """
        Override get_uid để xử lý trường hợp có nhiều endpoint với cùng root_path
        Trả về user_id của endpoint đầu tiên
        """
        records = self.search([("root_path", "=", root_path)])
        if not records:
            return None
        return records[0].user_id.id

    def _reset_app(self):
        """
        Reset app cache cho tất cả các endpoint có cùng root_path
        """
        # Clear cache cho root_path hiện tại
        self.get_app.clear_cache(self)
        self.get_uid.clear_cache(self)

        # Clear cache cho tất cả các endpoint khác có cùng root_path
        same_root_path_endpoints = self.search([
            ("root_path", "=", self.root_path),
            ("id", "!=", self.id)
        ])
        for endpoint in same_root_path_endpoints:
            self.get_app.clear_cache(endpoint)
            self.get_uid.clear_cache(endpoint)
            
            
    def write(self, vals):
        res = super().write(vals)
        # Reset cache khi có thay đổi quan trọng
        if any(field in vals for field in [ 'root_path', 'app', 'th_auth_method']):
            for rec in self:
                rec._reset_app()
        return res

    def _get_fastapi_app_dependencies(self) -> List[Depends]:
        """Return the dependencies to use for the fastapi app."""
        # Thêm head chứa lang và api key (trong phần hướng dẫn sử dụng)
        return [Depends(dependencies.accept_language), Depends(dependencies.accept_api_key)]


def th_api_key_based(
        api_key: Annotated[
            str, Depends(APIKeyHeader(name="api-key", description="In this demo, you can use a user's login as api key.")),
        ],
        request: Request,
        env: Annotated[Environment, Depends(odoo_env)],
) -> FastapiEndpoint:
    """
    api key của chính api đang được gọi vào đó
    """
    # Get the first part
    parts = [part for part in request.url.path.split('/') if part]
    first_part = '/' + parts[0] if parts else None

    # Kiểm tra xem api và root path có trên cùng 1 endpoint hay không
    th_api_key = (env["fastapi.endpoint"].sudo().search([("th_api_key", "=", api_key), ('root_path', '=', first_part)], limit=1))
    if not th_api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Incorrect API Key"
        )
    return th_api_key.with_context(th_api_key=api_key)
