from odoo import models, fields, api, _
from odoo.exceptions import UserError
import json


class ThApproveProject(models.Model):
    _name = 'th.approve.project'
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _description = "Phê duyệt dự án"
    _rec_name = 'th_project_lpm_id'

    th_project_lpm_id = fields.Many2one('th.project.lpm', string='Tên dự án')
    th_project_lpm_code = fields.Char(related="th_project_lpm_id.th_project_code", string="Mã dự án")
    th_project_scope = fields.Text(string="Phạm vi dự án", related="th_project_lpm_id.th_project_scope")
    th_implementation_unit = fields.Char(string="Đơn vị thực hiện",
                                         related="th_project_lpm_id.th_implementation_unit" )
    th_customers = fields.Many2one(related="th_project_lpm_id.th_university_id", string="Đ<PERSON><PERSON> tượng khách hàng")
    th_lead_the_project = fields.Many2one( string="Chủ trì dự án",
                                          related="th_project_lpm_id.th_lead_the_project", )
    th_project_members = fields.Many2many( string="Thành viên dự án",
                                          related="th_project_lpm_id.th_project_members", )
    th_start_date = fields.Date(string="Ngày bắt đầu",related="th_project_lpm_id.th_start_date")
    th_end_date = fields.Date(string="Ngày kết thúc",related="th_project_lpm_id.th_end_date",)
    th_production_number = fields.Integer(string="Số lượng môn", related="th_project_lpm_id.th_production_number")
    th_total_production_costs = fields.Integer(related="th_project_lpm_id.th_total_production_costs")
    th_product_manufacturing_ids = fields.One2many(related="th_project_lpm_id.th_product_manufacturing_ids", string="môn")
    th_user_approve_ids = fields.One2many('th.user.approve', 'th_approve_project_id', string='Người phê duyệt',)
    state = fields.Selection(
        selection=[('draft', 'Dự thảo'),
                   ('pending', 'Chờ phê duyệt'),
                   ('approved', 'Đã phê duyệt'),
                   ('refused', 'Bị từ chối'),
                   ('cancel', 'Hủy')],
        string="Trạng thái",
        default='draft', tracking=True
    ,compute='_compute_state', group_expand='_group_expand_states', store=True)
    th_production_standard_ids = fields.Many2many('th.production.standard',
                                                  string='Tiêu chuẩn sản xuất',compute='_compute_production_standard', tracking=True)
    th_total_proposed_costs = fields.Integer(string='Tổng chi phí đề xuất theo tín chỉ', related="th_project_lpm_id.th_total_proposed_costs")
    th_total_object_costs = fields.Integer(string='Tổng chi phí đề xuất theo môn', related="th_project_lpm_id.th_total_object_costs")
    th_hidden_button = fields.Boolean(string='ẩn nút', default=False, compute='_compute_th_hidden_button')
    th_cost_qa = fields.Float(string='Chi Phí QA',related="th_project_lpm_id.th_cost_qa")
    th_costs_incurred = fields.Float(string='Chi phí phát sinh',related="th_project_lpm_id.th_costs_incurred")
    # th_percent_costs_incurred = fields.Float(related="th_project_lpm_id.th_percent_costs_incurred")
    # th_selection_costs = fields.Selection(related="th_project_lpm_id.th_selection_costs")
    # th_percent_cost_qa = fields.Float(related="th_project_lpm_id.th_percent_cost_qa")
    th_state_approve = fields.Many2many("res.users", compute='_compute_th_state',string="Người đã phê duyệt")
    th_state_pending = fields.Many2many("res.users", compute='_compute_th_state',string="Người chưa phê duyệt")
    th_is_lpm2 = fields.Boolean(related="th_project_lpm_id.th_is_lpm2", string="Đã chuyển sang LPM2", default=False)
    
    @api.depends('th_user_approve_ids.status')
    def _compute_th_state(self):
        for record in self:
            approved_users = record.th_user_approve_ids.filtered(lambda r: r.status == 'approved')
            pending_users = record.th_user_approve_ids.filtered(lambda r: r.status == 'pending')
            record.th_state_approve = [(6,0,approved_users.mapped('th_user_id').ids)]
            record.th_state_pending = [(6,0,pending_users.mapped('th_user_id').ids)]


    @api.depends('th_production_standard_ids')
    def _compute_th_hidden_button(self):
        domain = self.th_user_approve_ids.mapped('th_user_id.id')
        for rec in self:
            rec.th_hidden_button =True if self.env.user.id in domain else False

    @api.depends('th_project_lpm_id')
    def _compute_production_standard(self):
        for rec in self:
            standard = []
            if rec.th_project_lpm_id.th_product_manufacturing_ids:
                # Lấy tất cả production standards và sắp xếp theo tên
                production_standards = rec.th_project_lpm_id.th_product_manufacturing_ids.mapped(
                    'th_production_standard_id')
                sorted_standards = production_standards.sorted(key=lambda r: r.th_standard_code)
                standard.extend(sorted_standards.ids)

            rec.th_production_standard_ids = standard if standard else [(5, 0, 0)]

    def action_approved(self):
        for rec in self.th_user_approve_ids:
            if self.env.user.id == rec.th_user_id.id:
                rec.status = 'approved'
        if not self.th_user_approve_ids.filtered(lambda d: d.status != 'approved'):
            self.state = 'approved'
        else:
            return True

    def action_refuse(self):
        for rec in self.th_user_approve_ids:
            rec.status = 'refused'
            self.state = 'refused'
    def action_draft(self):
        for rec in self.th_user_approve_ids:
            rec.status = 'draft'
        self.state = 'draft'
        return True



    def action_pending(self):
        for rec in self.th_user_approve_ids:
            rec.status = 'pending'
        self.state = 'pending'
        return True

    def action_cancel(self):
        for rec in self.th_user_approve_ids:
            rec.status = 'cancel'
        self.state = 'cancel'
        return True

    @api.depends("th_user_approve_ids")
    def _compute_state(self):
        for rec in self:
            if rec.th_user_approve_ids:
                statuses = self.th_user_approve_ids.mapped('status')
                if self.ids == False:
                    self.state = 'draft'
                elif 'refused' in statuses:
                    self.state = 'refused'
                elif all(status == 'approved' for status in statuses):
                    self.state = 'approved'

                else:
                    self.state = 'draft'
                # if rec.env.user in rec.th_user_approve_ids.mapped('th_user_id'):
                #     rec.state = rec.th_user_approve_ids.filtered(lambda d: d.th_user_id == rec.env.user)[0].status
                # else:
                #     for approver in rec.th_user_approve_ids:
                #         rec.state = approver.status
            else:
                rec.state = 'pending'
    def _group_expand_states(self, states, domain, order):
        return [key for key, val in type(self).state.selection]

    def write(self, values):
        res = super(ThApproveProject, self).write(values)
        for rec in self:
            if values.get('state') == 'approved':
                rec.th_project_lpm_id.th_approve = True
            elif 'state' in values and values.get('state') != 'approved':
                rec.th_project_lpm_id.th_approve = False
        return res

    @api.model_create_multi
    def create(self, values_list):
        # Add code here
        return super(ThApproveProject, self).create(values_list)

    def action_th_products_approve_manufacturing(self):
        for rec in self:
            action = self.env["ir.actions.actions"]._for_xml_id("th_lpm.th_approve_products_manufacturing_action")
            action['domain'] = [('id', 'in', rec.th_product_manufacturing_ids.ids)]
            action['context'] = {'default_th_project_lpm_id': rec.th_project_lpm_id.id,'create': False}
        return action
    
    
    def action_th_transfer_lpm2(self):
        pass
    
class ApprovalCategory(models.Model):
    _inherit = 'approval.category'

    def create_request_product(self):
        action = {
            "name": _("Phê duyệt sản phẩm"),
            "type": "ir.actions.act_window",
            "res_model": "approval.request",
            "views": [[self.env.ref("th_lpm.approval_request_product_view_tree").id, "tree"], [False, "form"]],
            "domain": [('category_id', "=",  self.env.ref('th_lpm.approval_product_category_data').id)],
            "context": {
                'create': True,
                'form_view_initial_mode': 'edit',
                'default_name': _('New') if self.automated_sequence else self.name,
                'default_category_id': self.env.ref('th_lpm.approval_product_category_data').id,
                'default_request_owner_id': self.env.user.id,
                'default_request_status': 'new'
            },
        }

        return action

