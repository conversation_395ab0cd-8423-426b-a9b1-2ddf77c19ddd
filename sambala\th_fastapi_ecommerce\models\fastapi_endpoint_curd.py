# Copyright 2022 ACSONE SA/NV
# License LGPL-3.0 or later (http://www.gnu.org/licenses/LGPL).
from typing import Annotated, Any, List

from a2wsgi import ASGIMiddleware

from odoo import _, api, fields, models, tools
from odoo.api import Environment
from odoo.exceptions import ValidationError


from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import APIKeyHeader

from ..dependencies import (odoo_env, fastapi_endpoint_impl)
from ..routers import sale_order_router
from fastapi.middleware.cors import CORSMiddleware
from .. import dependencies
from odoo.addons.th_fastapi.routers.b2c_srm import router as srm_b2c_router


class FastapiEndpoint(models.Model):
    _name = "fastapi.endpoint"
    _inherit = ["fastapi.endpoint", "th.intermediate.table"]

    th_auth_method = fields.Selection([("api_key", "Api Key")], string="Auth method")
    th_api_key = fields.Char(string="API Key")
    app = fields.Selection(selection_add=[('ec_v1', 'EC V1')], ondelete={"ec_v1": "cascade"})

    def _get_fastapi_routers(self) -> List[APIRouter]:
        # Trả về router đã định tuyến theo trường "app" được cấu hình
        routers = super()._get_fastapi_routers()
        if self.app == 'v1':
            return routers + [sale_order_router]
        if self.app == 'ec_v1':
            route_list = [sale_order_router]
            if srm_b2c_router:
                route_list.append(srm_b2c_router)
            return route_list
        return routers

    @api.model
    def _fastapi_app_fields(self) -> List[str]:
        field = super()._fastapi_app_fields()
        # Khi thêm 1 trường mới thì phải thêm ở đây(theo hướng dẫn của fastapi)
        field.append("th_auth_method")
        return field

    def _get_app(self):
        self.clear_caches()
        # check các link website gọi vào API xem có được phép gọi vào api hay không.
        app = super()._get_app()

        for router in self._get_fastapi_routers():
            app.include_router(router=router)
        app.dependency_overrides.update(self._get_app_dependencies_overrides())

        # Kiểm tra lại xem router có sử dụng phương thức bảo mật không (api_key)
        if self.app in ['ec_v1','v1']:
            auth_fast_endpoint = (
                th_api_key_based
            )
            app.dependency_overrides[
                fastapi_endpoint_impl
            ] = auth_fast_endpoint
        return app

    def write(self, vals):
        # cập nhập lại thông tin th_access_ids cho api
        res = super().write(vals)
        return res

    def _get_fastapi_app_dependencies(self) -> List[Depends]:
        """Return the dependencies to use for the fastapi app."""
        # Thêm head chứa lang và api key (trong phần hướng dẫn sử dụng)
        return [Depends(dependencies.accept_language), Depends(dependencies.accept_api_key)]


def th_api_key_based(
        api_key: Annotated[
            str, Depends(APIKeyHeader(name="api-key", description="In this demo, you can use a user's login as api key.")),
        ],
        request: Request,
        env: Annotated[Environment, Depends(odoo_env)],
) -> FastapiEndpoint:
    """
    api key của chính api đang được gọi vào đó
    """
    # Get the first part
    parts = [part for part in request.url.path.split('/') if part]
    first_part = '/' + parts[0] if parts else None

    # Kiểm tra xem api và root path có trên cùng 1 endpoint hay không
    th_api_key = (env["fastapi.endpoint"].sudo().search([("th_api_key", "=", api_key), ('root_path', '=', first_part)], limit=1))
    if not th_api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Incorrect API Key"
        )
    return th_api_key.with_context(th_api_key=api_key)
