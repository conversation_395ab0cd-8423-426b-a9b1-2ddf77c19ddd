Module Formio Tùy chỉnh (th_formio)
===================================

Tổng quan
---------

**Kế thừa module**:

- ``formio`` (<PERSON><PERSON><PERSON> gốc quản lý form)
- ``th_setup_parameters`` (<PERSON><PERSON><PERSON> tham số cấu hình)

**<PERSON><PERSON><PERSON> tiêu**:

Module ``th_formio`` được kế thừa và mở rộng các chức năng quản lý form trong module gốc ``formio``. Module này bổ sung các tính năng như quản lý trạng thái form nâng cao, tích hợp đồng bộ dữ liệu với hệ thống Sambala, quản lý đơn vị sở hữu, hỗ trợ đa ngôn ngữ và tùy chỉnh giao diện form.

Chức năng chỉnh sửa
------------------

1. Qu<PERSON>n lý Form Builder (FormioBuilder)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Model kế thừa**:

- ``formio.builder`` (từ module gốc ``formio``)

**Mục đích kế thừa**:

- Mở rộng model ``formio.builder`` để thêm các trường quản lý trạng thái form nâng cao, phân loại theo kho cơ hội (storage location), quản lý form theo đơn vị sở hữu và tích hợp đẩy dữ liệu sang các kho cơ hội APM, CRM, PRM.
- Tùy chỉnh quy trình phê duyệt form với các trạng thái TEST, IMPLEMENTATION_REQUEST, CURRENT.
- Cải tiến URL công khai và tích hợp iframe cho việc nhúng form.

**Logic hoặc hàm thay đổi**:

- **Hàm ghi đè/thay đổi**:
    - ``_compute_public_url``: Ghi đè để tạo URL công khai tùy chỉnh với iframe và script hỗ trợ.
    - ``create``: Ghi đè để tự động tạo tên form nếu không được cung cấp.
    - ``action_draft``, ``action_test``, ``action_current``: Các hàm quản lý trạng thái form.
    - ``get_public_builder``: Hàm xác thực quyền truy cập công khai.
    - ``compute_change_state``: Tính toán trạng thái dữ liệu demo dựa trên trạng thái form.

**View / Action / Menu**:

- **Views**:
    - ``th_formio_builder_form``: Form view tùy chỉnh với các trường mới và ẩn một số trường không cần thiết.
    - Kế thừa ``view_formio_builder_form`` từ module gốc, bổ sung các trường ``th_storage_location``, ``th_ownership_unit_id``, ``th_state_care``, ``th_origin_id``.

- **Actions**:
    - ``action_preview_form``: Hành động xem trước form với URL tùy chỉnh.

2. Quản lý Form Data (FormioForm)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Model kế thừa**:

- ``formio.form`` (từ module gốc ``formio``)

**Mục đích kế thừa**:

- Mở rộng model ``formio.form`` để thêm khả năng theo dõi trạng thái dữ liệu (dữ liệu test/dữ liệu thật), quản lý lỗi form, tích hợp đồng bộ dữ liệu với hệ thống Sambala .
- Hỗ trợ đẩy dữ liệu tự động sang các hệ thống khác thông qua API.

**Logic hoặc hàm thay đổi**:

- **Hàm mới được thêm**:
    - ``th_get_form_to_push``: Xử lý đẩy dữ liệu form sang hệ thống Sambala thông qua XML-RPC.
    - ``th_clean_old_pushed_forms``: Dọn dẹp các form cũ đã được đẩy dữ liệu (trên 90 ngày).

**View / Action / Menu**:

- **Views**:
    - ``view_formio_form_inherit``: Form view kế thừa, thêm hiển thị trạng thái đồng bộ và ẩn tab dữ liệu cho người dùng thường.

3. Cấu hình hệ thống (ResConfigSettings)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Model kế thừa**:

- ``res.config.settings`` (từ module gốc ``base``)

**Mục đích kế thừa**:

- Bổ sung các tham số cấu hình cho module formio như quản lý người duyệt form, cấu hình API server và các thiết lập tùy chỉnh khác.

**Logic hoặc hàm thay đổi**:

- **Hàm ghi đè/thay đổi**:
    - ``get_values``: Lấy giá trị cấu hình từ ``ir.config_parameter`` cho trường ``th_form_manager_ids``.
    - ``set_values``: Lưu giá trị cấu hình vào ``ir.config_parameter`` cho trường ``th_form_manager_ids``.

**View / Action / Menu**:

- **Views**:
    - ``th_res_config_settings_views``: Form view cấu hình tùy chỉnh.

4. Quản lý đơn vị sở hữu (ThOwnershipUnit)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Model kế thừa**:

- ``th.ownership.unit`` (từ module ``th_setup_parameters``)

**Mục đích kế thừa**:

- Mở rộng model ``th.ownership.unit`` để thêm khả năng quản lý website của đối tác và liên kết với các form.

**Logic hoặc hàm thay đổi**:

- **Các trường mới được thêm**:
    - ``th_partner_website_ids``: Liên kết One2many với ``th.partner.web``.

**View / Action / Menu**:

- **Views**:
    - ``th_partner_website.xml``: Views quản lý website đối tác.

5. Quản lý website đối tác (ThPartnerWebsite)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Model mới**:

- ``th.partner.web`` (model mới được tạo)

**Mục đích**:

- Quản lý thông tin website của đối tác, hỗ trợ tích hợp với form và theo dõi nguồn truy cập.

**Logic hoặc hàm thay đổi**:

- **Model mới**: Tạo hoàn toàn mới với các trường ``name``, ``th_state_id``, ``th_ownership_unit_id``.

**View / Action / Menu**:

- **Views**:
    - ``th_partner_website.xml``: Views quản lý website đối tác tuy nhiên chưa được sử dụng


6. Quản lý bản dịch (ThTranslates)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Model mới**:

- ``th.translate`` (model mới được tạo)

**Mục đích**:

- Quản lý bản dịch tùy chỉnh cho các form và giao diện, hỗ trợ đa ngôn ngữ.

**Logic hoặc hàm thay đổi**:

- **Model mới**: Tạo hoàn toàn mới với các trường ``lang_id``, ``th_key``, ``th_value``.

**View / Action / Menu**:

- **Views**:
    - ``th_translate.xml``: Views quản lý bản dịch.

7. Tùy chỉnh view formio (ThCustomViewFormio)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

**Model mới**:

- ``th.custom.view.formio`` (model mới được tạo)

**Mục đích**:

- Quản lý các view tùy chỉnh cho formio, ẩn cấu hình view form (phần js).

**Logic hoặc hàm thay đổi**:

- **Model mới**: Tạo hoàn toàn mới với các trường ``th_key``, ``th_value``, ``state``.

**View / Action / Menu**:

- **Views**:
    - ``th_custom_view_formio.xml``: Views quản lý cấu hình view tùy chỉnh.

View / Action / Menu (Tổng quan)
-------------------------------

- **Views**:
    - ``th_formio_builder_form``: Form view tùy chỉnh cho builder
    - ``view_formio_form_inherit``: Form view kế thừa cho form data
    - ``th_res_config_settings_views``: View cấu hình hệ thống
    - ``th_partner_website.xml``: Views quản lý website đối tác
    - ``th_translate.xml``: Views quản lý bản dịch
    - ``th_custom_view_formio.xml``: Views cấu hình view tùy chỉnh
    - ``formio_public_templates.xml``: Templates công khai tùy chỉnh
    - ``formio_menu.xml``: Menu tùy chỉnh

- **Actions**:
    - ``action_preview_form``: Hành động xem trước form
    - Các action kế thừa từ module gốc với tùy chỉnh

- **Menus**:
    - Kế thừa menu structure từ module ``formio`` gốc
    - Tùy chỉnh quyền truy cập và hiển thị menu

Data và Security
---------------

- **Data files**:
    - ``th_data_translate.xml``: Dữ liệu bản dịch mặc định
    - ``ir_cron.xml``: Cấu hình cron job cho đồng bộ dữ liệu

- **Security**:
    - ``ir.model.access.csv``: Quyền truy cập model
    - ``ir_model_access.xml``: Quyền truy cập chi tiết
    - ``security.xml``: Nhóm bảo mật
    - ``ir_rule.xml``: Quy tắc bảo mật record

Controllers
----------

- **Tùy chỉnh controllers**:
    - ``th_main.py``: Controller chính xử lý logic tùy chỉnh
    - Kế thừa ``main.py`` và ``public.py`` từ module gốc với các tùy chỉnh

Assets
------

- **Static files**:
    - JavaScript tùy chỉnh cho xử lý form
    - CSS tùy chỉnh cho giao diện
    - Thư viện hỗ trợ iframe và responsive

Tích hợp API
-----------

- Hỗ trợ đồng bộ dữ liệu với hệ thống Sambala thông qua XML-RPC
- Tích hợp với các module APM, CRM, PRM
- API endpoint tùy chỉnh cho việc submit form từ website công khai
